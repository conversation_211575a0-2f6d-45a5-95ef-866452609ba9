import AboutBrand from "@/components/coupons/about-brand";
import CouponBrand from "@/components/coupons/coupon-brand";
import CouponCategories from "@/components/coupons/coupon-categories";
import CouponsList from "@/components/coupons/coupons-list";
import SimilerBrands from "@/components/coupons/similer-brands";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

export const metadata = {
  title: "خصومات نون",
  description: "خصومات نون",
};

export default function BrandCouponPage() {
  return (
    <div className="pb-20">
      <div className="container">
        <Breadcrumb className="my-10" dir="rtl">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink
                href="/"
                className="text-[14px] font-[700] text-black lg:text-[16px]"
              >
                الرئيسية
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="rotate-180" />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-[14px] font-[700] text-[#646464] lg:text-[16px]">
                نون
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="grid grid-cols-1 gap-5 lg:grid-cols-2">
          <div className="flex flex-col gap-5">
            <CouponBrand />
            <CouponsList />
          </div>
          <div className="flex flex-col gap-5">
            <CouponCategories />
            <AboutBrand />
          </div>
          <div>
            <SimilerBrands />
          </div>
        </div>
      </div>
    </div>
  );
}
