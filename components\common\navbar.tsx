import Image from "next/image";
import Link from "next/link";

import add_store_image from "@/assets/add_store.svg";
import logo from "@/assets/logo.svg";

import { Button } from "../ui/button";

export default function Navbar() {
  return (
    <nav className="bg-white py-6 shadow">
      <div className="container flex items-center justify-between">
        <Link href={"/"}>
          <Image src={logo} alt="logo" />
        </Link>

        <Link href={"/add-store"}>
          <Button className="flex h-[40px] w-[130px] cursor-pointer items-center justify-center rounded-[8px] bg-black leading-[150%] font-[600] text-[#F5EFEC]">
            <Image
              src={add_store_image}
              alt="add store"
              className="h-[16px] w-[16px] object-cover"
              width={16}
              height={16}
            />
            أضف متجرك
          </Button>
        </Link>
      </div>
    </nav>
  );
}
