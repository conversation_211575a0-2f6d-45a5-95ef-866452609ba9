"use client";

import Image from "next/image";
import { Autoplay } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/autoplay";

import offers_1 from "@/assets/offers_1.svg";
import offers_2 from "@/assets/offers_2.svg";
import offers_3 from "@/assets/offers_3.svg";

const offersList = [
  {
    id: 1,
    image: offers_1,
  },
  {
    id: 2,
    image: offers_2,
  },
  {
    id: 3,
    image: offers_3,
  },
];

export function Offers() {
  return (
    <div className="relative container !px-0">
      <Swiper
        modules={[Autoplay]}
        loop={true}
        speed={500}
        autoplay={{
          delay: 2000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}
        slidesPerView={1}
        spaceBetween={16}
        dir="rtl"
        grabCursor={true}
        allowTouchMove={true}
        simulateTouch={true}
        touchRatio={1}
        touchAngle={45}
        longSwipes={true}
        shortSwipes={true}
        breakpoints={{
          320: {
            slidesPerView: 1,
            spaceBetween: 12,
          },
          480: {
            slidesPerView: 1,
            spaceBetween: 16,
          },
          640: {
            slidesPerView: 1.5,
            spaceBetween: 16,
          },
          768: {
            slidesPerView: 2,
            spaceBetween: 20,
          },
          1024: {
            slidesPerView: 2.5,
            spaceBetween: 24,
          },
          1280: {
            slidesPerView: 3,
            spaceBetween: 24,
          },
          1536: {
            slidesPerView: 3,
            spaceBetween: 32,
          },
        }}
        className="relative container my-14 w-full xl:my-20"
      >
        {offersList.map(({ id, image }) => (
          <SwiperSlide key={id} className="px-2 outline-none">
            <Image
              src={image}
              alt="offers"
              className="w-full cursor-pointer"
              priority
            />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
}
