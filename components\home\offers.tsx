"use client";

import Image from "next/image";
import Slider from "react-slick";

import offers_1 from "@/assets/offers_1.svg";
import offers_2 from "@/assets/offers_2.svg";
import offers_3 from "@/assets/offers_3.svg";

const offersList = [
  {
    id: 1,
    image: offers_1,
  },
  {
    id: 2,
    image: offers_2,
  },
  {
    id: 3,
    image: offers_3,
  },
];

export function Offers() {
  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    arrows: false,
    slidesToShow: 3,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 2000,
    rtl: true,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1,
        },
      },
    ],
  };

  return (
    <div className="relative container !px-0">
      <Slider
        {...settings}
        className="relative container my-14 w-full xl:my-20"
      >
        {offersList.map(({ id, image }) => (
          <div key={id} className="px-2 outline-none">
            <Image
              src={image}
              alt="offers"
              className="w-full cursor-pointer"
              priority
            />
          </div>
        ))}
      </Slider>
    </div>
  );
}
