"use client";

import Image from "next/image";
import Slider from "react-slick";

import { Button } from "@/components/ui/button";

import percentage_image from "@/assets/percentage.svg";
import phone_image from "@/assets/phone.svg";
import { ChevronLeft, ChevronRight } from "lucide-react";

const NextArrow = ({ onClick }: { onClick?: () => void }) => {
  return (
    <div
      onClick={onClick}
      className="absolute top-1/2 left-4 z-10 -translate-y-1/2"
    >
      <div className="ml-[40px] flex h-[40px] w-[40px] cursor-pointer items-center justify-center rounded-full bg-[#FFFFFF4D]">
        <ChevronLeft color="#fff" />
      </div>
    </div>
  );
};

const PrevArrow = ({ onClick }: { onClick?: () => void }) => {
  return (
    <div
      onClick={onClick}
      className="absolute top-1/2 right-4 z-10 -translate-y-1/2"
    >
      <div className="mr-[40px] flex h-[40px] w-[40px] cursor-pointer items-center justify-center rounded-full bg-[#FFFFFF4D]">
        <ChevronRight color="#fff" />
      </div>
    </div>
  );
};

export function Hero() {
  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    rtl: true,
    arrows: true,
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
  };

  return (
    <div className="relative container !px-0">
      <div className="relative">
        <Slider
          {...settings}
          className="relative container my-14 w-full xl:my-20"
        >
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index}>
              <div className="flex h-[120px] items-center justify-end rounded-2xl bg-gradient-to-t from-[#BE0909] via-[#D92F2F] to-[#FF6464] px-20 xl:h-[325px]">
                <div className="flex w-[180px] flex-col items-end gap-2 lg:w-auto xl:gap-8">
                  <Button
                    className="h-[27px] w-fit rounded-[100px] !bg-[#F6F6F6] p-[24px] text-[16px] leading-[72px] font-[600] text-black xl:h-[88px] xl:text-[40px]"
                    dir="rtl"
                  >
                    وفر أكثر مع خصوماتك! 🔥
                  </Button>
                  <h1 className="text-[14px] font-[600] text-white xl:text-[32px]">
                    أقوى العروض والتخفيضات من أفضل المتاجر والتطبيقات
                  </h1>
                </div>
              </div>
            </div>
          ))}
        </Slider>
      </div>

      <div>
        <Image
          src={phone_image}
          alt="phone"
          className="absolute bottom-0 left-28 w-[120px] xl:w-auto"
          priority
        />

        <Image
          src={percentage_image}
          alt="percentage"
          className="absolute right-[25%] bottom-5 w-[50px] lg:w-auto"
        />

        <Image
          src={percentage_image}
          alt="percentage"
          className="absolute top-5 left-[35%] w-[50px] lg:w-auto"
        />
      </div>
    </div>
  );
}
