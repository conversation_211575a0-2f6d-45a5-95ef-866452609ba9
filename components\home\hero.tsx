"use client";

import Image from "next/image";
import { EffectFade, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/effect-fade";
import "swiper/css/navigation";

import { Button } from "@/components/ui/button";

import percentage_image from "@/assets/percentage.svg";
import phone_image from "@/assets/phone.svg";
import { ChevronLeft, ChevronRight } from "lucide-react";

const NextArrow = () => {
  return (
    <div className="swiper-button-next-custom absolute top-1/2 left-4 z-10 -translate-y-1/2">
      <div className="ml-[40px] flex h-[40px] w-[40px] cursor-pointer items-center justify-center rounded-full bg-[#FFFFFF4D]">
        <ChevronLeft color="#fff" />
      </div>
    </div>
  );
};

const PrevArrow = () => {
  return (
    <div className="swiper-button-prev-custom absolute top-1/2 right-4 z-10 -translate-y-1/2">
      <div className="mr-[40px] flex h-[40px] w-[40px] cursor-pointer items-center justify-center rounded-full bg-[#FFFFFF4D]">
        <ChevronRight color="#fff" />
      </div>
    </div>
  );
};

export function Hero() {
  return (
    <div className="relative container !px-0">
      <div className="relative">
        <Swiper
          modules={[Navigation, EffectFade]}
          effect="fade"
          fadeEffect={{
            crossFade: true,
          }}
          navigation={{
            prevEl: ".swiper-button-prev-custom",
            nextEl: ".swiper-button-next-custom",
          }}
          loop={true}
          speed={500}
          dir="rtl"
          className="relative container my-14 w-full xl:my-20"
        >
          {Array.from({ length: 3 }).map((_, index) => (
            <SwiperSlide key={index}>
              <div className="flex h-[120px] items-center justify-start rounded-2xl bg-gradient-to-t from-[#BE0909] via-[#D92F2F] to-[#FF6464] px-20 xl:h-[325px]">
                <div className="flex w-[180px] flex-col items-start gap-2 lg:w-auto xl:gap-8">
                  <Button
                    className="h-[27px] w-fit rounded-[100px] !bg-[#F6F6F6] p-[24px] text-[16px] leading-[72px] font-[600] text-black xl:h-[88px] xl:text-[40px]"
                    // dir="rtl"
                  >
                    وفر أكثر مع خصوماتك! 🔥
                  </Button>
                  <h1 className="text-[14px] font-[600] text-white xl:text-[32px]">
                    أقوى العروض والتخفيضات من أفضل المتاجر والتطبيقات
                  </h1>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        {/* Custom Navigation Buttons */}
        <NextArrow />
        <PrevArrow />
      </div>

      <div>
        <Image
          src={phone_image}
          alt="phone"
          className="absolute bottom-0 left-28 w-[120px] xl:w-auto"
          priority
        />

        <Image
          src={percentage_image}
          alt="percentage"
          className="absolute right-[25%] bottom-5 w-[50px] lg:w-auto z-50"
        />

        <Image
          src={percentage_image}
          alt="percentage"
          className="absolute top-5 left-[35%] w-[50px] lg:w-auto"
        />
      </div>
    </div>
  );
}
