"use client";

import { useEffect } from "react";
import { onCLS, onFCP, onINP, onLCP, onTTFB } from "web-vitals";

export default function PerformanceMonitor() {
  useEffect(() => {
    // Report Web Vitals
    if (typeof window !== "undefined") {
      const reportWebVitals = async () => {
        try {
          // Report Core Web Vitals
          onCLS(console.log); // Cumulative Layout Shift
          onFCP(console.log); // First Contentful Paint
          onLCP(console.log); // Largest Contentful Paint
          onTTFB(console.log); // Time to First Byte
          onINP(console.log); // Interaction to Next Paint (replaces FID)
        } catch (error) {
          console.error("Failed to load web-vitals:", error);
        }
      };

      reportWebVitals();
    }

    // Monitor resource timing
    if (typeof window !== "undefined" && window.performance?.getEntriesByType) {
      try {
        const resources = window.performance.getEntriesByType("resource");
        console.log("Resource Timing:", resources);
      } catch (error) {
        console.error("Failed to get resource timing:", error);
      }
    }

    // Monitor long tasks
    if (typeof window !== "undefined" && window.PerformanceObserver) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            console.log("Long Task:", entry);
          }
        });

        observer.observe({ entryTypes: ["longtask"] });

        // Cleanup observer on unmount
        return () => {
          observer.disconnect();
        };
      } catch (error) {
        console.error("Failed to observe long tasks:", error);
      }
    }
  }, []);

  return null;
}
