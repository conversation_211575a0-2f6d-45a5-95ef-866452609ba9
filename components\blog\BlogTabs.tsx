"use client";

import { tabs } from "@/data";
import { cn } from "@/lib/utils";
import { useState } from "react";

export function BlogTabs() {
  const [selectedTab, setSelectedTab] = useState(1);

  return (
    <div className="flex flex-wrap items-center gap-10">
      {tabs.map(({ id, text }) => {
        return (
          <div
            key={id}
            className={cn(
              "cursor-pointer pb-2 text-[20px] font-[500] text-[#6F6F6F]",
              selectedTab === id && "border-b border-black text-black",
            )}
            onClick={() => setSelectedTab(id)}
          >
            {text}
          </div>
        );
      })}
    </div>
  );
}
