"use client";

import { ArrowUp } from "lucide-react";
import { useEffect, useState } from "react";

export default function ScrollToTop() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.scrollY > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener("scroll", toggleVisibility);

    return () => {
      window.removeEventListener("scroll", toggleVisibility);
    };
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  if (!isVisible) {
    return null;
  }

  return (
    <button
      onClick={scrollToTop}
      className="fixed bottom-20 left-8 z-50 flex h-[40px] w-[40px] cursor-pointer items-center justify-center rounded-full border bg-white text-white shadow transition-all hover:bg-gray-100 lg:h-[50px] lg:w-[50px]"
      aria-label="Scroll to top"
    >
      <ArrowUp className="size-5 lg:size-6" color="#000" />
    </button>
  );
}
