import { ChevronRight } from "lucide-react";
import Link from "next/link";

type BackButtonProps = {
  href: string;
  label: string;
};

export default function BackButton({ href, label }: BackButtonProps) {
  return (
    <Link href={href} className="flex items-center gap-2 font-[500]">
      <div className="flex h-[32px] w-[32px] items-center justify-center rounded-full bg-white shadow">
        <ChevronRight />
      </div>
      {label}
    </Link>
  );
}
