"use client";

import store_1 from "@/assets/store_1.svg";
import store_10 from "@/assets/store_10.svg";
import store_11 from "@/assets/store_11.svg";
import store_12 from "@/assets/store_12.svg";
import store_2 from "@/assets/store_2.svg";
import store_3 from "@/assets/store_3.svg";
import store_4 from "@/assets/store_4.svg";
import store_5 from "@/assets/store_5.svg";
import store_6 from "@/assets/store_6.svg";
import store_7 from "@/assets/store_7.svg";
import store_8 from "@/assets/store_8.svg";
import store_9 from "@/assets/store_9.svg";

import whats_app_image from "@/assets/whats.svg";

import { SearchIcon } from "lucide-react";
import Image from "next/image";
import { useMediaQuery } from "react-responsive";
import BrandItem from "../common/brand-item";
import MobileBrandItem from "../common/mobile-brand-item";
import { Button } from "../ui/button";

type Store = {
  id: number;
  image: string;
  title: string;
};

const stores: Store[] = [
  {
    id: 1,
    image: store_1,
    title: "نون",
  },
  {
    id: 2,
    image: store_2,
    title: "ترينديول",
  },
  {
    id: 3,
    image: store_3,
    title: "اي هيرب",
  },
  {
    id: 4,
    image: store_4,
    title: "نمشي",
  },
  {
    id: 5,
    image: store_5,
    title: "ممزورلد",
  },
  {
    id: 6,
    image: store_6,
    title: "6 ستريت",
  },
  {
    id: 7,
    image: store_7,
    title: "ايوا",
  },
  {
    id: 8,
    image: store_8,
    title: "سيار",
  },
  {
    id: 9,
    image: store_9,
    title: "تيمو",
  },
  {
    id: 10,
    image: store_10,
    title: "فورديل",
  },
  {
    id: 11,
    image: store_11,
    title: "ماكس",
  },
  {
    id: 12,
    image: store_12,
    title: "نعناع",
  },
];

const NoStores = () => {
  return (
    <div className="flex flex-col items-center justify-center gap-5 py-5">
      <SearchIcon color="#C9C9C9" className="size-[75px]" />
      <p className="text-[14px] font-[600] text-[#6E6E6E] lg:text-[16px]">
        المتجر اللي تبيه مو مضاف للحين 😔
      </p>
      <Button
        className="flex h-[60px] cursor-pointer gap-5 rounded-full border px-20 text-[14px] font-[600] lg:h-[70px] lg:text-[16px]"
        variant={"ghost"}
      >
        <Image src={whats_app_image} alt="whatsapp" />
        تواصل معنا وخلنا نضيفه! 🔔
      </Button>
    </div>
  );
};

export default function StoresList() {
  const isDesktop = useMediaQuery({
    query: "(min-width: 640px)",
  });

  if (stores.length === 0) {
    return <NoStores />;
  }

  if (isDesktop) {
    return (
      <div className="grid grid-cols-1 gap-7 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {stores.map(({ id, image, title }) => {
          return <BrandItem key={id} id={id} image={image} title={title} />;
        })}
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      {stores.map(({ id, image, title }) => {
        return <MobileBrandItem key={id} id={id} image={image} title={title} />;
      })}
    </div>
  );
}
