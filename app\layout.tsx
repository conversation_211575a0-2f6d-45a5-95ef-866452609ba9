import type { Metadata } from "next";

import Footer from "@/components/common/footer";
import Navbar from "@/components/common/navbar";
import ScrollToTop from "@/components/common/scroll-to-top";
import { ToastProvider } from "@/components/ui/toast-context";

import "@fontsource/ibm-plex-sans-arabic/100.css";
import "@fontsource/ibm-plex-sans-arabic/200.css";
import "@fontsource/ibm-plex-sans-arabic/300.css";
import "@fontsource/ibm-plex-sans-arabic/400.css";
import "@fontsource/ibm-plex-sans-arabic/500.css";
import "@fontsource/ibm-plex-sans-arabic/600.css";
import "@fontsource/ibm-plex-sans-arabic/700.css";

// tailwind css
import "./globals.css";

// react slick
import "slick-carousel/slick/slick-theme.css";
import "slick-carousel/slick/slick.css";

export const metadata: Metadata = {
  title: {
    default: "خصوماتك - أفضل منصة للخصومات والعروض",
    template: "%s | خصوماتك",
  },
  description:
    "منصة خصوماتك - أكبر تجمع للخصومات والعروض في السعودية. احصل على أحدث كوبونات الخصم وأكواد التخفيض من أشهر المتاجر المحلية والعالمية. وفر في مشترياتك مع خصومات حصرية تصل حتى 90%.",
  keywords: [
    "خصومات",
    "عروض",
    "كوبونات",
    "تخفيضات",
    "متاجر",
    "تسوق",
    "عروض خاصة",
    "كوبونات خصم",
    "أكواد تخفيض",
    "عروض حصرية",
    "تخفيضات السعودية",
  ],
  authors: [{ name: "خصوماتك" }],
  creator: "خصوماتك",
  publisher: "خصوماتك",
  icons: "/logo.svg",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning>
      <body>
        <ToastProvider>
          <Navbar />
          <main>{children}</main>
          <Footer />
          <ScrollToTop />
        </ToastProvider>
      </body>
    </html>
  );
}
