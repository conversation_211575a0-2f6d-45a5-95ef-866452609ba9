{"name": "khosomatk", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier . --write", "prepare": "husky"}, "dependencies": {"@fontsource/ibm-plex-sans-arabic": "^5.2.6", "@hookform/resolvers": "^5.1.0", "@mongez/react-atom": "^5.1.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@types/react-slick": "^0.23.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.513.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-responsive": "^10.0.1", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "tailwind-merge": "^3.3.0", "vaul": "^1.1.2", "web-vitals": "^5.0.3", "zod": "^3.25.56"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "husky": "^9.1.7", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}