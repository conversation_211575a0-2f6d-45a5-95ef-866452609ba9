import blog_facebook from "@/assets/blog_facebook.svg";
import blog_linkedin from "@/assets/blog_linkedin.svg";
import blog_phone from "@/assets/blog_phone.svg";
import blog_twitter from "@/assets/blog_twitter.svg";

import Image from "next/image";
import Link from "next/link";

import { Button } from "../ui/button";
import { Input } from "../ui/input";

export default function BlogDetailsFooter() {
  return (
    <div className="flex flex-col gap-10">
      <div className="flex flex-col items-center justify-between gap-5 border-t pt-5 lg:flex-row">
        <div>مشاركة</div>
        <div className="flex gap-2">
          <Link href={"/"}>
            <Image src={blog_facebook} alt="blog" />
          </Link>
          <Link href={"/"}>
            <Image src={blog_linkedin} alt="blog" />
          </Link>
          <Link href={"/"}>
            <Image src={blog_twitter} alt="blog" />
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-5 overflow-hidden rounded-[45px] bg-[#F6F6F6] p-10 lg:grid-cols-2 lg:pb-0">
        <Image src={blog_phone} alt="phone" className="m-auto rounded-t-4xl" />

        <div className="mt-10 flex flex-col gap-5">
          <h2 className="text-[32px] leading-[35px] font-[600]">
            لا تفوّت عليك شي 🔥
          </h2>
          <p className="leading-[40px] font-[400]">
            سجل بريدك وخلّك دايم أوّل من يدري عن أكواد الخصم والعروض الجديدة من
            خصوماتك. وفرنا لك كل شي جاهز… أنت بس اختار ووفّر!
          </p>

          <form className="relative h-[62px]">
            <Input
              type="email"
              placeholder="ادخل بريدك الالكتروني"
              className="h-full rounded-full border-0 bg-white px-5 text-[14px] lg:text-[16px]"
            />
            <Button
              type="submit"
              className="absolute top-1/2 left-3 h-[44px] -translate-y-1/2 cursor-pointer rounded-full px-5 lg:px-10"
            >
              اشترك
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
}
