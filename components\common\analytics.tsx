"use client";

import Script from "next/script";

interface AnalyticsProps {
  googleAnalyticsId?: string;
  microsoftClarityId?: string;
  hotjarId?: string;
}

export default function Analytics({
  googleAnalyticsId,
  microsoftClarityId,
  hotjarId,
}: AnalyticsProps) {
  if (!googleAnalyticsId && !microsoftClarityId && !hotjarId) {
    return null;
  }

  return (
    <>
      {/* Google Analytics */}
      {googleAnalyticsId && (
        <>
          <Script
            src={`https://www.googletagmanager.com/gtag/js?id=${googleAnalyticsId}`}
            strategy="afterInteractive"
            onError={(e) => {
              console.error("Failed to load Google Analytics:", e);
            }}
          />
          <Script id="google-analytics" strategy="afterInteractive">
            {`
              try {
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${googleAnalyticsId}');
              } catch (error) {
                console.error('Failed to initialize Google Analytics:', error);
              }
            `}
          </Script>
        </>
      )}

      {/* Microsoft Clarity */}
      {microsoftClarityId && (
        <Script
          id="microsoft-clarity"
          strategy="afterInteractive"
          onError={(e) => {
            console.error("Failed to load Microsoft Clarity:", e);
          }}
        >
          {`
            try {
              (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
              })(window, document, "clarity", "script", "${microsoftClarityId}");
            } catch (error) {
              console.error('Failed to initialize Microsoft Clarity:', error);
            }
          `}
        </Script>
      )}

      {/* Hotjar */}
      {hotjarId && (
        <Script
          id="hotjar"
          strategy="afterInteractive"
          onError={(e) => {
            console.error("Failed to load Hotjar:", e);
          }}
        >
          {`
            try {
              (function(h,o,t,j,a,r){
                h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                h._hjSettings={hjid:${hotjarId},hjsv:6};
                a=o.getElementsByTagName('head')[0];
                r=o.createElement('script');r.async=1;
                r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                a.appendChild(r);
              })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
            } catch (error) {
              console.error('Failed to initialize Hotjar:', error);
            }
          `}
        </Script>
      )}
    </>
  );
}
