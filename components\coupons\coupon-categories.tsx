import { Card } from "@/components/ui/card";
import { ChevronLeft } from "lucide-react";
import React from "react";

const categories = [
  ["أزياء", "مطاعم"],
  ["متاجر", "تجميل"],
  ["أثاث", "عطور"],
  ["مقاهي", "هدايا"],
];

export default function CouponCategories() {
  return (
    <Card className="flex-1 p-5 shadow-none lg:p-10">
      <h2 className="text-[24px] font-[600] lg:text-[32px]">الفئات</h2>
      <div className="grid grid-cols-2 gap-10 p-4 text-right">
        {categories.map((pair, index) => (
          <React.Fragment key={index}>
            {pair.map((item, idx) => (
              <div
                key={idx}
                className="flex items-center gap-3 border-b pb-2 text-[16px] font-[500] lg:text-[20px]"
              >
                <ChevronLeft className="text-muted-foreground h-4 w-4" />
                <span>{item}</span>
              </div>
            ))}
          </React.Fragment>
        ))}
      </div>
    </Card>
  );
}
