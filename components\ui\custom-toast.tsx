"use client";

import { CheckCircle, XCircle } from "lucide-react";
import { useEffect, useState } from "react";

interface CustomToastProps {
  message: string;
  type: "success" | "error";
  onClose: () => void;
}

export default function CustomToast({
  message,
  type,
  onClose,
}: CustomToastProps) {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onClose, 300); // Wait for fade out animation
    }, 2000);

    return () => clearTimeout(timer);
  }, [onClose]);

  if (!isVisible) return null;

  return (
    <div className="animate-fade-in fixed top-4 left-1/2 z-50 -translate-x-1/2">
      <div className="flex items-center gap-2 rounded-full bg-black px-4 py-2 text-sm text-white">
        <span>{message}</span>
        {type === "success" ? (
          <CheckCircle className="h-4 w-4 text-green-400" />
        ) : (
          <XCircle className="h-4 w-4 text-red-400" />
        )}
      </div>
    </div>
  );
}
