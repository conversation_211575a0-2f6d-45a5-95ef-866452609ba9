"use client";

import { ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import { Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";

import category_1 from "@/assets/category_1.svg";
import category_2 from "@/assets/category_2.svg";
import category_3 from "@/assets/category_3.svg";
import category_4 from "@/assets/category_4.svg";
import category_5 from "@/assets/category_5.svg";
import category_6 from "@/assets/category_6.svg";
import category_7 from "@/assets/category_7.svg";
import category_8 from "@/assets/category_8.svg";
import { cn } from "@/lib/utils";
import { useState } from "react";

const categoriesList = [
  {
    id: 1,
    image: category_1,
    title: "الكل",
  },
  {
    id: 2,
    image: category_2,
    title: "ترند",
  },
  {
    id: 3,
    image: category_3,
    title: "متاجر",
  },
  {
    id: 4,
    image: category_4,
    title: "مطاعم",
  },
  {
    id: 5,
    image: category_5,
    title: "تجميل",
  },
  {
    id: 6,
    image: category_6,
    title: "عطور",
  },
  {
    id: 7,
    image: category_7,
    title: "اثاث",
  },
  {
    id: 8,
    image: category_8,
    title: "هدايا",
  },
];

const NextArrow = () => {
  return (
    <div className="swiper-button-next-custom absolute top-1/2 left-4 z-10 -translate-y-1/2">
      <div className="group -ml-[30px] flex h-[40px] w-[40px] cursor-pointer items-center justify-center rounded-full bg-[#F6F6F6] transition-all duration-300 hover:bg-black">
        <ChevronLeft className="text-black transition-all duration-300 group-hover:text-white" />
      </div>
    </div>
  );
};

const PrevArrow = () => {
  return (
    <div className="swiper-button-prev-custom absolute top-1/2 right-4 z-10 -translate-y-1/2">
      <div className="group -mr-[30px] flex h-[40px] w-[40px] cursor-pointer items-center justify-center rounded-full bg-[#F6F6F6] transition-all duration-300 hover:bg-black">
        <ChevronRight className="text-black transition-all duration-300 group-hover:text-white" />
      </div>
    </div>
  );
};

export default function StoresCategories() {
  const [selectedCategoryId, setSelectedCategoryId] = useState(1);

  return (
    <div className="relative">
      <Swiper
        modules={[Navigation]}
        navigation={{
          prevEl: ".swiper-button-prev-custom",
          nextEl: ".swiper-button-next-custom",
        }}
        loop={true}
        speed={500}
        slidesPerView={8}
        spaceBetween={16}
        breakpoints={{
          480: {
            slidesPerView: 2,
          },
          768: {
            slidesPerView: 4,
          },
          1400: {
            slidesPerView: 6,
          },
          1600: {
            slidesPerView: 8,
          },
        }}
        className="relative container w-full"
      >
        {categoriesList.map(({ id, image, title }) => (
          <SwiperSlide
            key={id}
            className="!flex cursor-pointer flex-col items-center gap-2 px-2 outline-none"
            onClick={() => setSelectedCategoryId(id)}
          >
            <Image
              src={image}
              alt="offers"
              className={cn(
                "w-full",
                selectedCategoryId === id &&
                  "rounded-full border border-[#BE0909]",
              )}
              priority
            />
            <span
              className={cn(
                "rounded-full px-4 py-1 text-[14px] font-[600] lg:text-[18px]",
                selectedCategoryId === id && "bg-black text-white",
              )}
            >
              {title}
            </span>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom Navigation Buttons */}
      <NextArrow />
      <PrevArrow />
    </div>
  );
}
